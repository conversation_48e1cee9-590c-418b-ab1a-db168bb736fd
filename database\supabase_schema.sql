-- GPT-OSS-20B 聊天系统数据库表结构

-- 1. 聊天会话表
CREATE TABLE gpt_oss_20b_chat_sessions (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
    user_uuid VARCHAR(255) NOT NULL,
    title VARCHAR(255) DEFAULT 'New Chat',
    model_name VARCHAR(50) DEFAULT 'gpt-oss-20b',
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,

    -- 外键约束
    CONSTRAINT fk_gpt_oss_20b_chat_sessions_user FOREIGN KEY (user_uuid) REFERENCES users(uuid) ON DELETE CASCADE
);

-- 2. 聊天消息表
CREATE TABLE gpt_oss_20b_chat_messages (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
    session_uuid VARCHAR(255) NOT NULL,
    user_uuid VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    tokens_used INTEGER DEFAULT 0,
    credits_cost INTEGER DEFAULT 0,
    metadata TEXT DEFAULT '{}',
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,

    -- 外键约束
    CONSTRAINT fk_gpt_oss_20b_chat_messages_session FOREIGN KEY (session_uuid) REFERENCES gpt_oss_20b_chat_sessions(uuid) ON DELETE CASCADE,
    CONSTRAINT fk_gpt_oss_20b_chat_messages_user FOREIGN KEY (user_uuid) REFERENCES users(uuid) ON DELETE CASCADE
);

-- 3. 创建索引以提高查询性能
CREATE INDEX idx_gpt_oss_20b_chat_sessions_user_uuid ON gpt_oss_20b_chat_sessions(user_uuid);
CREATE INDEX idx_gpt_oss_20b_chat_sessions_updated_at ON gpt_oss_20b_chat_sessions(updated_at DESC);
CREATE INDEX idx_gpt_oss_20b_chat_messages_session_uuid ON gpt_oss_20b_chat_messages(session_uuid);
CREATE INDEX idx_gpt_oss_20b_chat_messages_created_at ON gpt_oss_20b_chat_messages(created_at);
CREATE INDEX idx_gpt_oss_20b_chat_messages_user_uuid ON gpt_oss_20b_chat_messages(user_uuid);

-- 4. 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_gpt_oss_20b_chat_sessions_updated_at
    BEFORE UPDATE ON gpt_oss_20b_chat_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 5. 行级安全策略 (RLS) - 暂时禁用以便测试
-- ALTER TABLE gpt_oss_20b_chat_sessions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE gpt_oss_20b_chat_messages ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的聊天会话 (暂时注释以便测试)
-- CREATE POLICY "Users can view own gpt_oss_20b chat sessions" ON gpt_oss_20b_chat_sessions
--     FOR SELECT USING (user_uuid = auth.uid()::text OR auth.role() = 'service_role');

-- CREATE POLICY "Users can insert own gpt_oss_20b chat sessions" ON gpt_oss_20b_chat_sessions
--     FOR INSERT WITH CHECK (user_uuid = auth.uid()::text OR auth.role() = 'service_role');

-- CREATE POLICY "Users can update own gpt_oss_20b chat sessions" ON gpt_oss_20b_chat_sessions
--     FOR UPDATE USING (user_uuid = auth.uid()::text OR auth.role() = 'service_role');

-- CREATE POLICY "Users can delete own gpt_oss_20b chat sessions" ON gpt_oss_20b_chat_sessions
--     FOR DELETE USING (user_uuid = auth.uid()::text OR auth.role() = 'service_role');

-- 用户只能访问自己的聊天消息 (暂时注释以便测试)
-- CREATE POLICY "Users can view own gpt_oss_20b chat messages" ON gpt_oss_20b_chat_messages
--     FOR SELECT USING (user_uuid = auth.uid()::text OR auth.role() = 'service_role');

-- CREATE POLICY "Users can insert own gpt_oss_20b chat messages" ON gpt_oss_20b_chat_messages
--     FOR INSERT WITH CHECK (user_uuid = auth.uid()::text OR auth.role() = 'service_role');

-- CREATE POLICY "Users can update own gpt_oss_20b chat messages" ON gpt_oss_20b_chat_messages
--     FOR UPDATE USING (user_uuid = auth.uid()::text OR auth.role() = 'service_role');

-- CREATE POLICY "Users can delete own gpt_oss_20b chat messages" ON gpt_oss_20b_chat_messages
--     FOR DELETE USING (user_uuid = auth.uid()::text OR auth.role() = 'service_role');

-- 6. 创建视图以便于查询
CREATE VIEW gpt_oss_20b_chat_sessions_with_stats AS
SELECT
    cs.*,
    COUNT(cm.id) as message_count,
    MAX(cm.created_at) as last_message_at,
    SUM(cm.credits_cost) as total_credits_used
FROM gpt_oss_20b_chat_sessions cs
LEFT JOIN gpt_oss_20b_chat_messages cm ON cs.uuid = cm.session_uuid
WHERE cs.is_deleted = FALSE
GROUP BY cs.id, cs.uuid, cs.user_uuid, cs.title, cs.model_name, cs.created_at, cs.updated_at, cs.is_deleted;

-- 7. 创建函数：获取用户的聊天会话列表
CREATE OR REPLACE FUNCTION get_gpt_oss_20b_user_chat_sessions(p_user_uuid VARCHAR(255), p_limit INTEGER DEFAULT 20, p_offset INTEGER DEFAULT 0)
RETURNS TABLE (
    id INTEGER,
    uuid VARCHAR(255),
    title VARCHAR(255),
    model_name VARCHAR(50),
    created_at timestamptz,
    updated_at timestamptz,
    message_count BIGINT,
    last_message_at timestamptz,
    total_credits_used BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        cs.id,
        cs.uuid,
        cs.title,
        cs.model_name,
        cs.created_at,
        cs.updated_at,
        cs.message_count,
        cs.last_message_at,
        cs.total_credits_used
    FROM gpt_oss_20b_chat_sessions_with_stats cs
    WHERE cs.user_uuid = p_user_uuid
    ORDER BY cs.updated_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. 创建函数：获取会话的消息列表
CREATE OR REPLACE FUNCTION get_gpt_oss_20b_session_messages(p_session_uuid VARCHAR(255), p_user_uuid VARCHAR(255))
RETURNS TABLE (
    id INTEGER,
    uuid VARCHAR(255),
    role VARCHAR(20),
    content TEXT,
    tokens_used INTEGER,
    credits_cost INTEGER,
    metadata TEXT,
    created_at timestamptz
) AS $$
BEGIN
    -- 验证用户是否有权限访问此会话
    IF NOT EXISTS (
        SELECT 1 FROM gpt_oss_20b_chat_sessions
        WHERE uuid = p_session_uuid AND user_uuid = p_user_uuid AND is_deleted = FALSE
    ) THEN
        RAISE EXCEPTION 'Session not found or access denied';
    END IF;

    RETURN QUERY
    SELECT
        cm.id,
        cm.uuid,
        cm.role,
        cm.content,
        cm.tokens_used,
        cm.credits_cost,
        cm.metadata,
        cm.created_at
    FROM gpt_oss_20b_chat_messages cm
    WHERE cm.session_uuid = p_session_uuid
    ORDER BY cm.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
