"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Send, Bot, User, Loader2, Plus, History, Clock } from "lucide-react";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

interface GptOss20bProps {
  title?: string;
}

export default function GptOss20b({
  title = "gpt-oss-20b"
}: GptOss20bProps = {}) {
  const { data: session } = useSession();
  const [mounted, setMounted] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [userCredits, setUserCredits] = useState<number | null>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [chatHeight, setChatHeight] = useState(400); // 默认高度 400px
  const [isResizing, setIsResizing] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [chatSessions, setChatSessions] = useState<any[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);

  useEffect(() => {
    setMounted(true);
    if (session) {
      fetchUserCredits();
    }
  }, [session]);

  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/credits');
      if (response.ok) {
        const data = await response.json();
        setUserCredits(data.left_credits);
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    if (!session) {
      toast.error('Please sign in to use the chat');
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsGenerating(true);

    try {
      const response = await fetch('/api/gpt-oss-20b/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          sessionId: currentSessionId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.error === "insufficient_credits") {
          toast.error('Insufficient credits. Please recharge and try again');
          return;
        }
        throw new Error(errorData.error || 'Failed to send message');
      }

      // 获取会话ID（如果是新会话）
      const sessionId = response.headers.get('X-Session-Id');
      if (sessionId && !currentSessionId) {
        setCurrentSessionId(sessionId);
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: "",
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);

      const decoder = new TextDecoder();
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;

        if (value) {
          const chunk = decoder.decode(value);
          
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessage.id 
                ? { ...msg, content: msg.content + chunk }
                : msg
            )
          );
        }
      }

      // Update user credits after successful message
      fetchUserCredits();

    } catch (error) {
      console.error('Chat error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to send message');
      
      // Remove the user message if there was an error
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startNewSession = () => {
    setMessages([]);
    setCurrentSessionId(null);
    setShowHistory(false);
    toast.success('Started new chat session');
  };

  // 获取聊天记录列表
  const fetchChatHistory = async () => {
    if (!session) return;

    setIsLoadingHistory(true);
    try {
      const response = await fetch('/api/chat/sessions');
      if (response.ok) {
        const data = await response.json();
        setChatSessions(data.sessions || []);
      } else {
        toast.error('Failed to load chat history');
      }
    } catch (error) {
      console.error('Failed to fetch chat history:', error);
      toast.error('Failed to load chat history');
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // 加载特定会话的消息
  const loadSession = async (sessionUuid: string) => {
    try {
      const response = await fetch(`/api/chat/sessions/${sessionUuid}/messages`);
      if (response.ok) {
        const data = await response.json();
        const historyMessages: Message[] = data.messages.map((msg: any) => ({
          id: msg.uuid,
          role: msg.role,
          content: msg.content,
          timestamp: new Date(msg.created_at)
        }));
        setMessages(historyMessages);
        setCurrentSessionId(sessionUuid);
        setShowHistory(false);
        toast.success('Chat history loaded');
      } else {
        toast.error('Failed to load chat messages');
      }
    } catch (error) {
      console.error('Failed to load session:', error);
      toast.error('Failed to load chat messages');
    }
  };

  // 切换历史记录显示
  const toggleHistory = () => {
    if (!showHistory) {
      fetchChatHistory();
    }
    setShowHistory(!showHistory);
  };

  // 拖拽调整高度的处理函数
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);

    const startY = e.clientY;
    const startHeight = chatHeight;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaY = e.clientY - startY;
      const newHeight = Math.max(200, Math.min(800, startHeight + deltaY));
      setChatHeight(newHeight);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 mb-8">
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <h2 className="text-2xl font-semibold">{title}</h2>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleHistory}
                className="h-6 px-2 text-green-600 hover:text-green-700 hover:bg-green-50"
                title="View chat history"
              >
                <History className="w-3 h-3 mr-1" />
                History
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={startNewSession}
                className="h-6 px-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                title="Start a new conversation"
              >
                <Plus className="w-3 h-3 mr-1" />
                New Chat
              </Button>
            </div>
          </div>
          <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full flex items-center gap-2">
            <span className="text-sm font-normal">Credits:</span>
            {mounted && session ? (
              <span className="text-lg font-bold">{userCredits !== null ? userCredits : "Loading..."}</span>
            ) : (
              <span className="text-lg font-bold">
                <a href="/auth/signin" className="hover:underline">Sign in</a>
              </span>
            )}
          </div>
        </div>

        {/* Unlogged user prompt */}
        {mounted && !session && (
          <div className="text-center py-2 bg-blue-50 rounded-lg mb-4">
            <a
              href="/auth/signin"
              className="text-blue-600 hover:text-blue-800 underline text-sm"
            >
              <strong>Sign in for free trial</strong>
            </a>
          </div>
        )}

        {/* Chat History */}
        {showHistory && (
          <div className="mb-4 border rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-medium">Chat History</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowHistory(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </Button>
            </div>

            {isLoadingHistory ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                Loading chat history...
              </div>
            ) : chatSessions.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No chat history found
              </div>
            ) : (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {chatSessions.map((session: any) => (
                  <div
                    key={session.uuid}
                    className="flex items-center justify-between p-3 bg-white rounded border hover:bg-gray-50 cursor-pointer"
                    onClick={() => loadSession(session.uuid)}
                  >
                    <div className="flex-1">
                      <div className="font-medium text-sm truncate">
                        {session.title}
                      </div>
                      <div className="text-xs text-gray-500 flex items-center mt-1">
                        <Clock className="w-3 h-3 mr-1" />
                        {new Date(session.updated_at).toLocaleDateString()} {new Date(session.updated_at).toLocaleTimeString()}
                      </div>
                    </div>
                    <div className="text-xs text-gray-400 ml-2">
                      {session.message_count || 0} messages
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Chat interface */}
        <div className="space-y-4">
          <div className="relative">
            <div
              className="w-full border rounded-lg p-4 overflow-y-auto"
              style={{ height: `${chatHeight}px` }}
            >
            {messages.length === 0 ? (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <Bot className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>Start a conversation with gpt-oss-20b</p>
                  <p className="text-sm mt-1">Ask me anything!</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      message.role === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    {message.role === "assistant" && (
                      <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <Bot className="w-4 h-4 text-blue-600" />
                      </div>
                    )}
                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-2 ${
                        message.role === "user"
                          ? "bg-blue-600 text-white"
                          : "bg-gray-100 text-gray-900"
                      }`}
                    >
                      <p className="whitespace-pre-wrap">{message.content}</p>
                      <p className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                    {message.role === "user" && (
                      <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0">
                        <User className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                ))}
                {isGenerating && (
                  <div className="flex gap-3 justify-start">
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                      <Bot className="w-4 h-4 text-blue-600" />
                    </div>
                    <div className="bg-gray-100 rounded-lg px-4 py-2">
                      <div className="flex items-center gap-2">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span className="text-sm text-gray-600">Thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
            </div>

            {/* 拖拽手柄 */}
            <div
              className={`w-full h-2 cursor-ns-resize flex items-center justify-center transition-colors ${
                isResizing ? 'bg-blue-500' : 'bg-gray-300 hover:bg-gray-400'
              }`}
              onMouseDown={handleMouseDown}
              title={`拖拽调整高度 (当前: ${chatHeight}px)`}
            >
              <div className="w-8 h-1 bg-white rounded-full opacity-70"></div>
            </div>
          </div>

          {/* Input area */}
          <div className="flex gap-2">
            <Textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message here..."
              className="flex-1 min-h-[60px] max-h-[200px] resize-y"
              disabled={isGenerating || !session}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isGenerating || !session}
              size="lg"
              className="px-6"
            >
              {isGenerating ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
