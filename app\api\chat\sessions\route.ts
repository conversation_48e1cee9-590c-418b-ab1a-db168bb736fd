import { NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { ChatService } from "@/services/chat";

// 获取用户的聊天会话列表
export async function GET(req: Request) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    const sessions = await ChatService.getUserSessions(userUuid, limit, offset);

    return NextResponse.json({
      sessions,
      pagination: {
        limit,
        offset,
        hasMore: sessions.length === limit
      }
    });

  } catch (error) {
    console.error('Error fetching chat sessions:', error);
    return NextResponse.json(
      { error: "Failed to fetch chat sessions" },
      { status: 500 }
    );
  }
}

// 创建新的聊天会话
export async function POST(req: Request) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { title } = await req.json();

    const session = await ChatService.createSession(userUuid, title);

    return NextResponse.json({ session }, { status: 201 });

  } catch (error) {
    console.error('Error creating chat session:', error);
    return NextResponse.json(
      { error: "Failed to create chat session" },
      { status: 500 }
    );
  }
}
