import { NextResponse } from "next/server";
import Replicate from "replicate";
import { getUserUuid } from "@/services/user";
import { getUserCredits, decreaseCredits, CreditsTransType } from "@/services/credit";
import { ChatService } from "@/services/chat";

export const runtime = 'edge';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function POST(req: Request) {
  try {
    const { message, sessionId } = await req.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: "Message is required" },
        { status: 400 }
      );
    }

    // Get user UUID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check user credits
    const userCredits = await getUserCredits(userUuid);
    if (userCredits.left_credits < 1) {
      return NextResponse.json(
        { error: "insufficient_credits" },
        { status: 402 }
      );
    }

    // 处理会话ID - 如果没有提供则创建新会话
    let currentSessionId = sessionId;
    if (!currentSessionId) {
      try {
        const newSession = await ChatService.createSession(
          userUuid,
          ChatService.generateSessionTitle(message)
        );
        currentSessionId = newSession.uuid;
      } catch (error) {
        console.error("Failed to create chat session:", error);
        return NextResponse.json(
          { error: "Failed to create chat session" },
          { status: 500 }
        );
      }
    }

    // 保存用户消息到数据库
    let userMessageId: string;
    try {
      const userMessageRecord = await ChatService.addMessage(
        currentSessionId,
        userUuid,
        'user',
        message,
        0, // tokens_used - 用户消息不消耗tokens
        1, // credits_cost - 每条消息1积分
        { model: 'gpt-oss-20b' }
      );
      userMessageId = userMessageRecord.uuid;
    } catch (error) {
      console.error("Failed to save user message:", error);
      return NextResponse.json(
        { error: "Failed to save message" },
        { status: 500 }
      );
    }

    // Deduct credits (1 credit per message)
    try {
      await decreaseCredits({
        user_uuid: userUuid,
        trans_type: CreditsTransType.Chat,
        credits: 1,
      });
    } catch (error) {
      console.error("Failed to deduct credits:", error);
      return NextResponse.json(
        { error: "Failed to process payment" },
        { status: 500 }
      );
    }

    // Prepare input for Replicate API
    const input = {
      top_p: 1,
      prompt: message,
      max_tokens: 1024,
      temperature: 0.1,
      presence_penalty: 0,
      frequency_penalty: 0
    };

    try {
      // Create a readable stream for the response
      let assistantResponse = '';

      const stream = new ReadableStream({
        async start(controller) {
          try {
            // Use Replicate's streaming API
            for await (const event of replicate.stream("openai/gpt-oss-20b", { input })) {
              const chunk = event.toString();
              assistantResponse += chunk;
              controller.enqueue(new TextEncoder().encode(chunk));
            }

            // 保存 AI 回复到数据库
            try {
              await ChatService.addMessage(
                currentSessionId,
                userUuid,
                'assistant',
                assistantResponse,
                0, // tokens_used - 可以后续计算
                0, // credits_cost - AI回复不额外收费
                {
                  model: 'gpt-oss-20b',
                  user_message_id: userMessageId,
                  response_length: assistantResponse.length
                }
              );
            } catch (dbError) {
              console.error("Failed to save assistant message:", dbError);
              // 不中断流，但记录错误
            }

            controller.close();
          } catch (error) {
            console.error("Replicate streaming error:", error);
            controller.error(error);
          }
        },
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Transfer-Encoding': 'chunked',
          'X-Session-Id': currentSessionId, // 返回会话ID给前端
        },
      });

    } catch (replicateError) {
      console.error('Replicate API error:', replicateError);
      
      // If Replicate fails, we should refund the credits
      try {
        await decreaseCredits({
          user_uuid: userUuid,
          trans_type: CreditsTransType.Refund,
          credits: -1, // Negative to add credits back
        });
      } catch (refundError) {
        console.error("Failed to refund credits:", refundError);
      }

      return NextResponse.json(
        { 
          error: replicateError instanceof Error ? replicateError.message : "Failed to connect to AI service"
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
