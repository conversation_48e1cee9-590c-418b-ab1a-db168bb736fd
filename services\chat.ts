import { supabaseAdmin, GptOss20bChatSession, GptOss20bChatMessage, GptOss20bChatSessionWithStats } from '@/lib/supabase';

export class ChatService {
  /**
   * 创建新的聊天会话
   */
  static async createSession(userUuid: string, title?: string): Promise<GptOss20bChatSession> {
    const { data, error } = await supabaseAdmin
      .from('gpt_oss_20b_chat_sessions')
      .insert({
        user_uuid: userUuid,
        title: title || 'New Chat',
        model_name: 'gpt-oss-20b'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating chat session:', error);
      throw new Error('Failed to create chat session');
    }

    return data;
  }

  /**
   * 获取用户的聊天会话列表
   */
  static async getUserSessions(
    userUuid: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<GptOss20bChatSessionWithStats[]> {
    const { data, error } = await supabaseAdmin
      .rpc('get_gpt_oss_20b_user_chat_sessions', {
        p_user_uuid: userUuid,
        p_limit: limit,
        p_offset: offset
      });

    if (error) {
      console.error('Error fetching user sessions:', error);
      throw new Error('Failed to fetch chat sessions');
    }

    return data || [];
  }

  /**
   * 获取特定会话的消息列表
   */
  static async getSessionMessages(sessionUuid: string, userUuid: string): Promise<GptOss20bChatMessage[]> {
    const { data, error } = await supabaseAdmin
      .rpc('get_gpt_oss_20b_session_messages', {
        p_session_uuid: sessionUuid,
        p_user_uuid: userUuid
      });

    if (error) {
      console.error('Error fetching session messages:', error);
      throw new Error('Failed to fetch messages');
    }

    return data || [];
  }

  /**
   * 添加消息到会话
   */
  static async addMessage(
    sessionUuid: string,
    userUuid: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    tokensUsed: number = 0,
    creditsCost: number = 0,
    metadata: Record<string, any> = {}
  ): Promise<GptOss20bChatMessage> {
    const { data, error } = await supabaseAdmin
      .from('gpt_oss_20b_chat_messages')
      .insert({
        session_uuid: sessionUuid,
        user_uuid: userUuid,
        role,
        content,
        tokens_used: tokensUsed,
        credits_cost: creditsCost,
        metadata: JSON.stringify(metadata)
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding message:', error);
      throw new Error('Failed to add message');
    }

    // 更新会话的 updated_at 时间
    await supabaseAdmin
      .from('gpt_oss_20b_chat_sessions')
      .update({ updated_at: new Date().toISOString() })
      .eq('uuid', sessionUuid)
      .eq('user_uuid', userUuid);

    return data;
  }

  /**
   * 更新会话标题
   */
  static async updateSessionTitle(sessionUuid: string, userUuid: string, title: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('gpt_oss_20b_chat_sessions')
      .update({ title })
      .eq('uuid', sessionUuid)
      .eq('user_uuid', userUuid);

    if (error) {
      console.error('Error updating session title:', error);
      throw new Error('Failed to update session title');
    }
  }

  /**
   * 删除会话（软删除）
   */
  static async deleteSession(sessionUuid: string, userUuid: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('gpt_oss_20b_chat_sessions')
      .update({ is_deleted: true })
      .eq('uuid', sessionUuid)
      .eq('user_uuid', userUuid);

    if (error) {
      console.error('Error deleting session:', error);
      throw new Error('Failed to delete session');
    }
  }

  /**
   * 获取会话详情
   */
  static async getSession(sessionUuid: string, userUuid: string): Promise<GptOss20bChatSession | null> {
    const { data, error } = await supabaseAdmin
      .from('gpt_oss_20b_chat_sessions')
      .select('*')
      .eq('uuid', sessionUuid)
      .eq('user_uuid', userUuid)
      .eq('is_deleted', false)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Session not found
      }
      console.error('Error fetching session:', error);
      throw new Error('Failed to fetch session');
    }

    return data;
  }

  /**
   * 清空会话的所有消息
   */
  static async clearSessionMessages(sessionUuid: string, userUuid: string): Promise<void> {
    // 验证用户是否拥有此会话
    const session = await this.getSession(sessionUuid, userUuid);
    if (!session) {
      throw new Error('Session not found or access denied');
    }

    const { error } = await supabaseAdmin
      .from('gpt_oss_20b_chat_messages')
      .delete()
      .eq('session_uuid', sessionUuid)
      .eq('user_uuid', userUuid);

    if (error) {
      console.error('Error clearing session messages:', error);
      throw new Error('Failed to clear messages');
    }

    // 更新会话的 updated_at 时间
    await supabaseAdmin
      .from('gpt_oss_20b_chat_sessions')
      .update({ updated_at: new Date().toISOString() })
      .eq('uuid', sessionUuid)
      .eq('user_uuid', userUuid);
  }

  /**
   * 根据消息内容自动生成会话标题
   */
  static generateSessionTitle(firstMessage: string): string {
    // 取前50个字符作为标题，如果太长则截断并添加省略号
    const maxLength = 50;
    const title = firstMessage.trim();
    
    if (title.length <= maxLength) {
      return title;
    }
    
    return title.substring(0, maxLength).trim() + '...';
  }

  /**
   * 获取用户的聊天统计信息
   */
  static async getUserChatStats(userUuid: string): Promise<{
    totalSessions: number;
    totalMessages: number;
    totalCreditsUsed: number;
  }> {
    const { data: sessionStats, error: sessionError } = await supabaseAdmin
      .from('gpt_oss_20b_chat_sessions')
      .select('id')
      .eq('user_uuid', userUuid)
      .eq('is_deleted', false);

    const { data: messageStats, error: messageError } = await supabaseAdmin
      .from('gpt_oss_20b_chat_messages')
      .select('credits_cost')
      .eq('user_uuid', userUuid);

    if (sessionError || messageError) {
      console.error('Error fetching chat stats:', sessionError || messageError);
      throw new Error('Failed to fetch chat statistics');
    }

    const totalCreditsUsed = messageStats?.reduce((sum, msg) => sum + (msg.credits_cost || 0), 0) || 0;

    return {
      totalSessions: sessionStats?.length || 0,
      totalMessages: messageStats?.length || 0,
      totalCreditsUsed
    };
  }
}
