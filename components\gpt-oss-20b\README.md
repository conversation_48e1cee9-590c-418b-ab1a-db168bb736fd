# GPT-OSS-20B Chat Component

This component provides a chat interface for the gpt-oss-20b language model using Replicate's API.

## Features

- Real-time chat interface with streaming responses
- Credit-based usage system
- User authentication integration
- Responsive design with modern UI
- Message history display
- Auto-scroll to latest messages

## API Integration

The component integrates with Replicate's gpt-oss-20b model:
- Model: `openai/gpt-oss-20b`
- Streaming responses for real-time chat experience
- Configurable parameters (temperature, max_tokens, etc.)

## Usage

```tsx
import GptOss20b from "@/components/gpt-oss-20b";

// Basic usage
<GptOss20b />

// With custom title
<GptOss20b title="Custom Chat Bot" />
```

## Props

- `title` (optional): Custom title for the chat component (default: "gpt-oss-20b")

## API Endpoints

- `POST /api/gpt-oss-20b/chat` - Send chat messages
- `GET /api/credits` - Get user credits

## Credit System

- Each message costs 1 credit
- Users must be authenticated to use the chat
- Insufficient credits will redirect to pricing page
- Failed requests automatically refund credits

## Dependencies

- Replicate API for model access
- NextAuth for authentication
- Sonner for toast notifications
- Lucide React for icons
- Tailwind CSS for styling
