import { NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { ChatService } from "@/services/chat";

// 获取会话的消息列表
export async function GET(
  req: Request,
  { params }: { params: { sessionId: string } }
) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const messages = await ChatService.getSessionMessages(params.sessionId, userUuid);

    return NextResponse.json({ messages });

  } catch (error) {
    console.error('Error fetching session messages:', error);
    return NextResponse.json(
      { error: "Failed to fetch messages" },
      { status: 500 }
    );
  }
}

// 清空会话的所有消息
export async function DELETE(
  req: Request,
  { params }: { params: { sessionId: string } }
) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    await ChatService.clearSessionMessages(params.sessionId, userUuid);

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error clearing session messages:', error);
    return NextResponse.json(
      { error: "Failed to clear messages" },
      { status: 500 }
    );
  }
}
