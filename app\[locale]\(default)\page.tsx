import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import HeroWithCompare from "@/components/blocks/hero-with-compare";
import ImageCompareGallery from "@/components/blocks/image-compare-gallery";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import { Generator4o } from "@/components/generator4o";
import { Kontext } from "@/components/kontext";
import KontextDev from "@/components/kontextdev";
import GptOss20b from "@/components/gpt-oss-20b/simple";
import { Button } from "@/components/ui/button";
import Link from "next/link";


export const runtime = "edge";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  // These can be updated with the URLs provided by the user
  const compareImages = {
    originalSrc: "https://img.gpt-oss-20b.top/data-comparison-chart-1.webp",
    modifiedSrc: "https://img.gpt-oss-20b.top/data-comparison-chart-2.webp",
    beforeText: "Codeforce Competition Code",
    afterText: "Competition Math"
  };

  // 示例图片对比数据 - 这些应该替换为实际的图片URL
  const compareGroups = [
    {
      id: 1,
      originalSrc: "https://img.gpt-oss-20b.top/coding-assistance-1.webp",
      modifiedSrc: "https://img.gpt-oss-20b.top/coding-assistance-2.webp",
      alt: "Generate and debug Python code instantly with GPT OSS 20B's tool‑calling support.",
      beforeText: "Generate and debug",
      afterText: "Python code"
    },
    {
      id: 2,
      originalSrc: "https://img.gpt-oss-20b.top/educational-tutoring-1.webp",
      modifiedSrc: "https://img.gpt-oss-20b.top/educational-tutoring-2.webp",
      alt: "Provide step-by-step explanations for math, science, and language learning.",
      beforeText: "For Math, science",
      afterText: "Language Learning"
    },
    {
      id: 3,
      originalSrc: "https://img.gpt-oss-20b.top/customer-support-automation-1.webp",
      modifiedSrc: "https://img.gpt-oss-20b.top/customer-support-automation-2.webp",
      alt: "Deliver fast, accurate replies to customer queries with natural, human-like conversations.",
      beforeText: "Deliver fast",
      afterText: "Human-like conversations"
    },
    {
      id: 4,
      originalSrc: "https://img.gpt-oss-20b.top/travel-planning-1.webp",
      modifiedSrc: "https://img.gpt-oss-20b.top/travel-planning-2.webp",
      alt: "Generate detailed itineraries, including attractions, routes, and recommendations.",
      beforeText: "Generate detailed itineraries",
      afterText: "Attractions, Routes"
    },
    {
      id: 5,
      originalSrc: "https://img.gpt-oss-20b.top/financial-analysis-1.webp",
      modifiedSrc: "https://img.gpt-oss-20b.top/financial-analysis-2.webp",
      alt: "Summarize market trends and create investment insights in real time.",
      beforeText: "Summarize market trends",
      afterText: "Create investment insights"
    },
    {
      id: 6,
      originalSrc: "https://img.gpt-oss-20b.top/creative-writing-1.webp",
      modifiedSrc: "https://img.gpt-oss-20b.top/creative-writing-2.webp",
      alt: "Write engaging stories, scripts, and articles with coherent, context-aware content.",
      beforeText: "Write engaging stories",
      afterText: "Scripts, and articles"
    }
  ];

  return (
    <>
      {page.hero && <HeroWithCompare
        hero={page.hero}
        compareImages={compareImages}
      />}
      <GptOss20b />
      
      
      
      
      {/* 图片对比画廊 */}
      <ImageCompareGallery 
        title="Explore GPT-OSS-20B in Action - Real-World Use Cases"
        description="Discover how GPT-OSS-20B can be applied across diverse real-world scenarios. From coding assistance and creative writing to customer support and complex reasoning, this open-source 21B parameter LLM delivers fast, private, and efficient results—try it instantly in our free online playground."
        compareGroups={compareGroups} 
      />

      {page.branding && <Branding section={page.branding} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      
      
      
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      
      {/* Pricing Section - Just a button to pricing page */}
      <div className="container py-12 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Ready to Get Started?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Explore our pricing plans and find the perfect option for your creative needs
          </p>
          <Link href="/pricing">
            <Button size="lg" className="gap-2">
              View Pricing Plans
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>
      
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
      
    </>
  );
}
