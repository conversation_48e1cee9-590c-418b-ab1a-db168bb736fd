import { createClient } from '@supabase/supabase-js';

// 客户端 Supabase 实例（用于前端）
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 服务端 Supabase 实例（用于 API 路由，具有更高权限）
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// 数据库类型定义
export interface GptOss20bChatSession {
  id: number;
  uuid: string;
  user_uuid: string;
  title: string;
  model_name: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

export interface GptOss20bChatMessage {
  id: number;
  uuid: string;
  session_uuid: string;
  user_uuid: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  tokens_used: number;
  credits_cost: number;
  metadata: string;
  created_at: string;
}

export interface GptOss20bChatSessionWithStats extends GptOss20bChatSession {
  message_count: number;
  last_message_at: string | null;
  total_credits_used: number;
}
