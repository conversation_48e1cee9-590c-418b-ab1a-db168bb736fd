import { NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { ChatService } from "@/services/chat";

// 获取特定会话的详情
export async function GET(
  req: Request,
  { params }: { params: { sessionId: string } }
) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const session = await ChatService.getSession(params.sessionId, userUuid);
    if (!session) {
      return NextResponse.json(
        { error: "Session not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ session });

  } catch (error) {
    console.error('Error fetching chat session:', error);
    return NextResponse.json(
      { error: "Failed to fetch chat session" },
      { status: 500 }
    );
  }
}

// 更新会话信息
export async function PUT(
  req: Request,
  { params }: { params: { sessionId: string } }
) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { title } = await req.json();

    if (!title || typeof title !== 'string') {
      return NextResponse.json(
        { error: "Title is required" },
        { status: 400 }
      );
    }

    await ChatService.updateSessionTitle(params.sessionId, userUuid, title);

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error updating chat session:', error);
    return NextResponse.json(
      { error: "Failed to update chat session" },
      { status: 500 }
    );
  }
}

// 删除会话
export async function DELETE(
  req: Request,
  { params }: { params: { sessionId: string } }
) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    await ChatService.deleteSession(params.sessionId, userUuid);

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error deleting chat session:', error);
    return NextResponse.json(
      { error: "Failed to delete chat session" },
      { status: 500 }
    );
  }
}
