-- 清理和重新创建 GPT-OSS-20B 聊天系统数据库表

-- 1. 删除现有的策略（如果存在）
DROP POLICY IF EXISTS "Users can view own gpt_oss_20b chat sessions" ON gpt_oss_20b_chat_sessions;
DROP POLICY IF EXISTS "Users can insert own gpt_oss_20b chat sessions" ON gpt_oss_20b_chat_sessions;
DROP POLICY IF EXISTS "Users can update own gpt_oss_20b chat sessions" ON gpt_oss_20b_chat_sessions;
DROP POLICY IF EXISTS "Users can delete own gpt_oss_20b chat sessions" ON gpt_oss_20b_chat_sessions;

DROP POLICY IF EXISTS "Users can view own gpt_oss_20b chat messages" ON gpt_oss_20b_chat_messages;
DROP POLICY IF EXISTS "Users can insert own gpt_oss_20b chat messages" ON gpt_oss_20b_chat_messages;
DROP POLICY IF EXISTS "Users can update own gpt_oss_20b chat messages" ON gpt_oss_20b_chat_messages;
DROP POLICY IF EXISTS "Users can delete own gpt_oss_20b chat messages" ON gpt_oss_20b_chat_messages;

-- 2. 删除现有的函数（如果存在）
DROP FUNCTION IF EXISTS get_gpt_oss_20b_user_chat_sessions(VARCHAR, INTEGER, INTEGER);
DROP FUNCTION IF EXISTS get_gpt_oss_20b_session_messages(VARCHAR, VARCHAR);

-- 3. 删除现有的视图（如果存在）
DROP VIEW IF EXISTS gpt_oss_20b_chat_sessions_with_stats;

-- 4. 删除现有的表（如果存在）
DROP TABLE IF EXISTS gpt_oss_20b_chat_messages;
DROP TABLE IF EXISTS gpt_oss_20b_chat_sessions;

-- 5. 重新创建表结构（不启用RLS）

-- 聊天会话表
CREATE TABLE gpt_oss_20b_chat_sessions (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
    user_uuid VARCHAR(255) NOT NULL,
    title VARCHAR(255) DEFAULT 'New Chat',
    model_name VARCHAR(50) DEFAULT 'gpt-oss-20b',
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    
    -- 外键约束
    CONSTRAINT fk_gpt_oss_20b_chat_sessions_user FOREIGN KEY (user_uuid) REFERENCES users(uuid) ON DELETE CASCADE
);

-- 聊天消息表
CREATE TABLE gpt_oss_20b_chat_messages (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
    session_uuid VARCHAR(255) NOT NULL,
    user_uuid VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    tokens_used INTEGER DEFAULT 0,
    credits_cost INTEGER DEFAULT 0,
    metadata TEXT DEFAULT '{}',
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_gpt_oss_20b_chat_messages_session FOREIGN KEY (session_uuid) REFERENCES gpt_oss_20b_chat_sessions(uuid) ON DELETE CASCADE,
    CONSTRAINT fk_gpt_oss_20b_chat_messages_user FOREIGN KEY (user_uuid) REFERENCES users(uuid) ON DELETE CASCADE
);

-- 6. 创建索引以提高查询性能
CREATE INDEX idx_gpt_oss_20b_chat_sessions_user_uuid ON gpt_oss_20b_chat_sessions(user_uuid);
CREATE INDEX idx_gpt_oss_20b_chat_sessions_updated_at ON gpt_oss_20b_chat_sessions(updated_at DESC);
CREATE INDEX idx_gpt_oss_20b_chat_messages_session_uuid ON gpt_oss_20b_chat_messages(session_uuid);
CREATE INDEX idx_gpt_oss_20b_chat_messages_created_at ON gpt_oss_20b_chat_messages(created_at);
CREATE INDEX idx_gpt_oss_20b_chat_messages_user_uuid ON gpt_oss_20b_chat_messages(user_uuid);

-- 7. 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_gpt_oss_20b_chat_sessions_updated_at 
    BEFORE UPDATE ON gpt_oss_20b_chat_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. 创建视图以便于查询
CREATE VIEW gpt_oss_20b_chat_sessions_with_stats AS
SELECT 
    cs.*,
    COUNT(cm.id) as message_count,
    MAX(cm.created_at) as last_message_at,
    SUM(cm.credits_cost) as total_credits_used
FROM gpt_oss_20b_chat_sessions cs
LEFT JOIN gpt_oss_20b_chat_messages cm ON cs.uuid = cm.session_uuid
WHERE cs.is_deleted = FALSE
GROUP BY cs.id, cs.uuid, cs.user_uuid, cs.title, cs.model_name, cs.created_at, cs.updated_at, cs.is_deleted;

-- 9. 创建函数：获取用户的聊天会话列表
CREATE OR REPLACE FUNCTION get_gpt_oss_20b_user_chat_sessions(p_user_uuid VARCHAR(255), p_limit INTEGER DEFAULT 20, p_offset INTEGER DEFAULT 0)
RETURNS TABLE (
    id INTEGER,
    uuid VARCHAR(255),
    title VARCHAR(255),
    model_name VARCHAR(50),
    created_at timestamptz,
    updated_at timestamptz,
    message_count BIGINT,
    last_message_at timestamptz,
    total_credits_used BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cs.id,
        cs.uuid,
        cs.title,
        cs.model_name,
        cs.created_at,
        cs.updated_at,
        cs.message_count,
        cs.last_message_at,
        cs.total_credits_used
    FROM gpt_oss_20b_chat_sessions_with_stats cs
    WHERE cs.user_uuid = p_user_uuid
    ORDER BY cs.updated_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. 创建函数：获取会话的消息列表
CREATE OR REPLACE FUNCTION get_gpt_oss_20b_session_messages(p_session_uuid VARCHAR(255), p_user_uuid VARCHAR(255))
RETURNS TABLE (
    id INTEGER,
    uuid VARCHAR(255),
    role VARCHAR(20),
    content TEXT,
    tokens_used INTEGER,
    credits_cost INTEGER,
    metadata TEXT,
    created_at timestamptz
) AS $$
BEGIN
    -- 验证用户是否有权限访问此会话
    IF NOT EXISTS (
        SELECT 1 FROM gpt_oss_20b_chat_sessions 
        WHERE uuid = p_session_uuid AND user_uuid = p_user_uuid AND is_deleted = FALSE
    ) THEN
        RAISE EXCEPTION 'Session not found or access denied';
    END IF;
    
    RETURN QUERY
    SELECT 
        cm.id,
        cm.uuid,
        cm.role,
        cm.content,
        cm.tokens_used,
        cm.credits_cost,
        cm.metadata,
        cm.created_at
    FROM gpt_oss_20b_chat_messages cm
    WHERE cm.session_uuid = p_session_uuid
    ORDER BY cm.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 注意：暂时不启用 RLS，以便测试功能
-- 功能测试通过后，可以根据需要启用 RLS

COMMENT ON TABLE gpt_oss_20b_chat_sessions IS 'GPT-OSS-20B 聊天会话表 - RLS 暂时禁用用于测试';
COMMENT ON TABLE gpt_oss_20b_chat_messages IS 'GPT-OSS-20B 聊天消息表 - RLS 暂时禁用用于测试';
