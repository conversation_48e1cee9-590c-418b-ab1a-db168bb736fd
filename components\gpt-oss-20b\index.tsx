"use client";

import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Send, Bot, User, Loader2, Trash2, MessageSquare, Plus } from "lucide-react";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

interface GptOss20bProps {
  title?: string;
}

export default function GptOss20b({
  title = "GPT-OSS-20B"
}: GptOss20bProps = {}) {
  const { data: session } = useSession();
  const router = useRouter();
  const [inputMessage, setInputMessage] = useState("");
  const [userCredits, setUserCredits] = useState<number | null>(null);
  const [mounted, setMounted] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [chatHeight, setChatHeight] = useState(384); // 默认 h-96 = 384px
  const [isResizing, setIsResizing] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const resizeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setMounted(true);
    
    // Fetch user credits if logged in
    if (session) {
      fetchUserCredits();
    }
  }, [session]);

  useEffect(() => {
    if (messages.length > 0) {
      // 添加小延迟确保DOM更新完成
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [messages]);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  // 拖拽调整高度功能
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const container = messagesContainerRef.current;
      if (!container) return;
      
      const rect = container.getBoundingClientRect();
      const newHeight = e.clientY - rect.top;
      
      // 限制最小和最大高度
      const minHeight = 200;
      const maxHeight = 800;
      
      if (newHeight >= minHeight && newHeight <= maxHeight) {
        setChatHeight(newHeight);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/credits');
      if (response.ok) {
        const data = await response.json();
        setUserCredits(data.left_credits);
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error);
    }
  };

  const loadSessionHistory = async (sessionId: string) => {
    setIsLoadingHistory(true);
    try {
      const response = await fetch(`/api/chat/sessions/${sessionId}/messages`);
      if (response.ok) {
        const data = await response.json();
        const historyMessages: Message[] = data.messages.map((msg: any) => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          timestamp: new Date(msg.created_at)
        }));
        setMessages(historyMessages);
        setCurrentSessionId(sessionId);
      }
    } catch (error) {
      console.error('Failed to load session history:', error);
      toast.error('Failed to load chat history');
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const startNewSession = () => {
    setMessages([]);
    setCurrentSessionId(null);
    toast.success('Started new chat session');
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    if (!session) {
      toast.error('Please sign in to use the chat');
      router.push('/auth/signin');
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsGenerating(true);

    try {
      const response = await fetch('/api/gpt-oss-20b/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          sessionId: currentSessionId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.error === "insufficient_credits") {
          toast.error('Insufficient credits. Please recharge and try again');
          router.push('/pricing');
          return;
        }
        throw new Error(errorData.error || 'Failed to send message');
      }

      // 获取会话ID（如果是新会话）
      const sessionId = response.headers.get('X-Session-Id');
      if (sessionId && !currentSessionId) {
        setCurrentSessionId(sessionId);
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: "",
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);

      const decoder = new TextDecoder();
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;

        if (value) {
          const chunk = decoder.decode(value);
          
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessage.id 
                ? { ...msg, content: msg.content + chunk }
                : msg
            )
          );
        }
      }

      // Update user credits after successful message
      fetchUserCredits();

    } catch (error) {
      console.error('Chat error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to send message');
      
      // Remove the user message if there was an error
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearMessages = async () => {
    if (messages.length > 0) {
      const confirmed = window.confirm('确定要清空所有聊天记录吗？');
      if (confirmed) {
        try {
          if (currentSessionId) {
            // 清空数据库中的消息
            const response = await fetch(`/api/chat/sessions/${currentSessionId}/messages`, {
              method: 'DELETE',
            });

            if (!response.ok) {
              throw new Error('Failed to clear messages from database');
            }
          }

          setMessages([]);
          toast.success('聊天记录已清空');
        } catch (error) {
          console.error('Failed to clear messages:', error);
          toast.error('清空聊天记录失败');
        }
      }
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 mb-8">
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <h2 className="text-2xl font-semibold">{title}</h2>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              {messages.length > 0 && (
                <>
                  <MessageSquare className="w-4 h-4" />
                  <span>{messages.length} 条消息</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearMessages}
                    className="h-6 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                    title="清空聊天记录"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={startNewSession}
                className="h-6 px-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                title="开始新对话"
              >
                <Plus className="w-3 h-3 mr-1" />
                新对话
              </Button>
            </div>
          </div>
          <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full flex items-center gap-2">
            <span className="text-sm font-normal">Credits:</span>
            {mounted && session ? (
              <span className="text-lg font-bold">{userCredits !== null ? userCredits : "Loading..."}</span>
            ) : (
              <span className="text-lg font-bold">
                <a href="/auth/signin" className="hover:underline">Sign in</a>
              </span>
            )}
            <svg
              className="w-4 h-4 text-amber-500 fill-current"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
            >
              <path d="M12 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0-15c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h12c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9zm0 3c3.31 0 6 2.69 6 6v7c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-7c0-3.31 2.69-6 6-6z" />
            </svg>
          </div>
        </div>

        {/* Unlogged user prompt */}
        {mounted && !session && (
          <div className="text-center py-2 bg-blue-50 rounded-lg mb-4">
            <a
              href="/auth/signin"
              className="text-blue-600 hover:text-blue-800 underline text-sm"
            >
              <strong>Sign in for free trial</strong>
            </a>
          </div>
        )}

        {/* Chat interface */}
        <div className="space-y-4">
          <div className="relative">
            <div 
              ref={messagesContainerRef} 
              className="w-full border rounded-lg p-4 overflow-y-auto relative"
              style={{ height: `${chatHeight}px` }}
            >
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <Bot className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Start a conversation with gpt-oss-20b</p>
                    <p className="text-sm mt-1">Ask me anything!</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex gap-3 ${
                        message.role === "user" ? "justify-end" : "justify-start"
                      }`}
                    >
                      {message.role === "assistant" && (
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                          <Bot className="w-4 h-4 text-blue-600" />
                        </div>
                      )}
                      <div
                        className={`max-w-[80%] rounded-lg px-4 py-2 ${
                          message.role === "user"
                            ? "bg-blue-600 text-white"
                            : "bg-gray-100 text-gray-900"
                        }`}
                      >
                        <p className="whitespace-pre-wrap">{message.content}</p>
                        <p className="text-xs opacity-70 mt-1">
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                      {message.role === "user" && (
                        <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0">
                          <User className="w-4 h-4 text-white" />
                        </div>
                      )}
                    </div>
                  ))}
                  {isGenerating && (
                    <div className="flex gap-3 justify-start">
                      <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <Bot className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="bg-gray-100 rounded-lg px-4 py-2">
                        <div className="flex items-center gap-2">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span className="text-sm text-gray-600">Thinking...</span>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>
            
            {/* 拖拽调整高度的手柄 */}
            <div 
              ref={resizeRef}
              className={`absolute bottom-0 left-0 right-0 h-2 cursor-row-resize bg-gray-200 hover:bg-gray-300 transition-colors ${
                isResizing ? 'bg-blue-400' : ''
              }`}
              onMouseDown={handleMouseDown}
              title={`拖拽调整聊天窗口高度 (当前: ${chatHeight}px)`}
            >
              <div className="absolute inset-x-0 top-1/2 transform -translate-y-1/2 h-0.5 bg-gray-400 mx-4"></div>
              {isResizing && (
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 bg-white px-1 rounded">
                  {chatHeight}px
                </div>
              )}
            </div>
          </div>

          {/* Input area */}
          <div className="flex gap-2">
            <Textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message here..."
              className="flex-1 min-h-[60px] max-h-[200px] resize-y"
              disabled={isGenerating || !session}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isGenerating || !session}
              size="lg"
              className="px-6"
            >
              {isGenerating ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
